from datetime import datetime
from typing import Optional

def user_entity(user) -> dict:
    """Convert MongoDB document to dictionary"""
    return {
        "id": str(user["_id"]),
        "name": user["name"],
        "email": user["email"],
        "age": user.get("age"),
        "phone": user.get("phone"),
        "address": user.get("address"),
        "created_at": user["created_at"],
        "updated_at": user["updated_at"]
    }

def users_entity(users) -> list:
    """Convert list of MongoDB documents to list of dictionaries"""
    return [user_entity(user) for user in users]

def user_create_entity(user_data: dict) -> dict:
    """Prepare user data for MongoDB insertion"""
    now = datetime.utcnow()
    return {
        "name": user_data["name"],
        "email": user_data["email"],
        "password": user_data["password"],  # In production, hash this password
        "age": user_data.get("age"),
        "phone": user_data.get("phone"),
        "address": user_data.get("address"),
        "created_at": now,
        "updated_at": now
    }
