from pydantic import BaseModel, Field, EmailStr
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    """Base user model"""
    name: str = Field(..., min_length=1, max_length=100, description="User's full name")
    email: EmailStr = Field(..., description="User's email address")
    age: Optional[int] = Field(None, ge=0, le=150, description="User's age")
    phone: Optional[str] = Field(None, min_length=10, max_length=15, description="User's phone number")
    address: Optional[str] = Field(None, max_length=200, description="User's address")

class UserCreate(UserBase):
    """Model for creating a new user"""
    password: str = Field(..., min_length=6, description="User's password")

class UserUpdate(BaseModel):
    """Model for updating user information"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    email: Optional[EmailStr] = None
    age: Optional[int] = Field(None, ge=0, le=150)
    phone: Optional[str] = Field(None, min_length=10, max_length=15)
    address: Optional[str] = Field(None, max_length=200)

class UserResponse(UserBase):
    """Model for user response (without password)"""
    id: str = Field(..., alias="_id", description="User's unique identifier")
    created_at: datetime = Field(..., description="User creation timestamp")
    updated_at: datetime = Field(..., description="User last update timestamp")
    
    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "name": "John Doe",
                "email": "<EMAIL>",
                "age": 30,
                "phone": "+1234567890",
                "address": "123 Main St, City, Country",
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00"
            }
        }
