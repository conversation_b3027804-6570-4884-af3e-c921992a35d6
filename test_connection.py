import asyncio
import os
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_mongodb_connection():
    """Test MongoDB connection"""
    try:
        # Connect to MongoDB
        client = AsyncIOMotorClient(os.getenv("MONGODB_URL", "mongodb://localhost:27017"))
        
        # Test the connection
        await client.admin.command('ping')
        print("✅ Successfully connected to MongoDB!")
        
        # Get database
        db = client[os.getenv("DATABASE_NAME", "fastapi_mongodb")]
        print(f"✅ Database '{db.name}' is accessible")
        
        # Test collection operations
        collection = db.users
        
        # Insert a test document
        test_user = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpass",
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00"
        }
        
        result = await collection.insert_one(test_user)
        print(f"✅ Test document inserted with ID: {result.inserted_id}")
        
        # Find the document
        found_user = await collection.find_one({"_id": result.inserted_id})
        print(f"✅ Test document found: {found_user['name']}")
        
        # Delete the test document
        delete_result = await collection.delete_one({"_id": result.inserted_id})
        print(f"✅ Test document deleted: {delete_result.deleted_count} document(s)")
        
        # Close connection
        client.close()
        print("✅ MongoDB connection test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error connecting to MongoDB: {e}")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(test_mongodb_connection())
