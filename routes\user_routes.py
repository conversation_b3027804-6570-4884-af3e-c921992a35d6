from fastapi import APIRouter, HTTPException, Query, status
from typing import List

from models.user import UserCreate, UserUpdate, UserResponse
from services.user_service import user_service

router = APIRouter(prefix="/users", tags=["Users"])

@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(user: UserCreate):
    """
    Create a new user
    
    - **name**: User's full name (required)
    - **email**: User's email address (required, must be unique)
    - **password**: User's password (required, minimum 6 characters)
    - **age**: User's age (optional)
    - **phone**: User's phone number (optional)
    - **address**: User's address (optional)
    """
    try:
        return await user_service.create_user(user)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=List[UserResponse])
async def get_all_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of users to return")
):
    """
    Get all users with pagination
    
    - **skip**: Number of users to skip (default: 0)
    - **limit**: Maximum number of users to return (default: 100, max: 1000)
    """
    try:
        return await user_service.get_all_users(skip=skip, limit=limit)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/count")
async def get_users_count():
    """Get total number of users"""
    try:
        count = await user_service.get_users_count()
        return {"total_users": count}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{user_id}", response_model=UserResponse)
async def get_user_by_id(user_id: str):
    """
    Get user by ID
    
    - **user_id**: User's unique identifier
    """
    try:
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/email/{email}", response_model=UserResponse)
async def get_user_by_email(email: str):
    """
    Get user by email
    
    - **email**: User's email address
    """
    try:
        user = await user_service.get_user_by_email(email)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{user_id}", response_model=UserResponse)
async def update_user(user_id: str, user_update: UserUpdate):
    """
    Update user information
    
    - **user_id**: User's unique identifier
    - **name**: User's full name (optional)
    - **email**: User's email address (optional)
    - **age**: User's age (optional)
    - **phone**: User's phone number (optional)
    - **address**: User's address (optional)
    """
    try:
        updated_user = await user_service.update_user(user_id, user_update)
        if not updated_user:
            raise HTTPException(status_code=404, detail="User not found")
        return updated_user
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{user_id}")
async def delete_user(user_id: str):
    """
    Delete user by ID
    
    - **user_id**: User's unique identifier
    """
    try:
        deleted = await user_service.delete_user(user_id)
        if not deleted:
            raise HTTPException(status_code=404, detail="User not found")
        return {"message": "User deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
