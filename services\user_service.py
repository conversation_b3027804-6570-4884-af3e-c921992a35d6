from typing import List, Optional
from bson import ObjectId
from datetime import datetime
from pymongo.errors import DuplicateKeyError

from config.database import get_database
from models.user import UserCreate, UserUpdate, UserResponse
from schemas.user_schema import user_entity, users_entity, user_create_entity

class UserService:
    def __init__(self):
        self.db = None
        self.collection = None

    def _get_collection(self):
        """Get collection instance"""
        if self.db is None:
            self.db = get_database()
            self.collection = self.db.users
        return self.collection
    
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """Create a new user"""
        try:
            collection = self._get_collection()
            # Check if email already exists
            existing_user = await collection.find_one({"email": user_data.email})
            if existing_user:
                raise ValueError("Email already exists")

            # Prepare user data for insertion
            user_dict = user_create_entity(user_data.dict())

            # Insert user into database
            result = await collection.insert_one(user_dict)

            # Retrieve the created user
            created_user = await collection.find_one({"_id": result.inserted_id})
            
            return UserResponse(**user_entity(created_user))
            
        except DuplicateKeyError:
            raise ValueError("Email already exists")
        except Exception as e:
            raise Exception(f"Error creating user: {str(e)}")
    
    async def get_user_by_id(self, user_id: str) -> Optional[UserResponse]:
        """Get user by ID"""
        try:
            if not ObjectId.is_valid(user_id):
                return None

            collection = self._get_collection()
            user = await collection.find_one({"_id": ObjectId(user_id)})
            if user:
                return UserResponse(**user_entity(user))
            return None
            
        except Exception as e:
            raise Exception(f"Error retrieving user: {str(e)}")
    
    async def get_user_by_email(self, email: str) -> Optional[UserResponse]:
        """Get user by email"""
        try:
            collection = self._get_collection()
            user = await collection.find_one({"email": email})
            if user:
                return UserResponse(**user_entity(user))
            return None
            
        except Exception as e:
            raise Exception(f"Error retrieving user: {str(e)}")
    
    async def get_all_users(self, skip: int = 0, limit: int = 100) -> List[UserResponse]:
        """Get all users with pagination"""
        try:
            collection = self._get_collection()
            cursor = collection.find().skip(skip).limit(limit)
            users = await cursor.to_list(length=limit)
            return [UserResponse(**user_entity(user)) for user in users]
            
        except Exception as e:
            raise Exception(f"Error retrieving users: {str(e)}")
    
    async def update_user(self, user_id: str, user_data: UserUpdate) -> Optional[UserResponse]:
        """Update user information"""
        try:
            if not ObjectId.is_valid(user_id):
                return None

            collection = self._get_collection()
            # Prepare update data (only include non-None fields)
            update_data = {k: v for k, v in user_data.dict().items() if v is not None}

            if not update_data:
                # No data to update
                return await self.get_user_by_id(user_id)

            # Add updated timestamp
            update_data["updated_at"] = datetime.utcnow()

            # Check if email is being updated and already exists
            if "email" in update_data:
                existing_user = await collection.find_one({
                    "email": update_data["email"],
                    "_id": {"$ne": ObjectId(user_id)}
                })
                if existing_user:
                    raise ValueError("Email already exists")

            # Update user
            result = await collection.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": update_data}
            )
            
            if result.modified_count == 0:
                return None
            
            # Return updated user
            return await self.get_user_by_id(user_id)
            
        except ValueError as e:
            raise e
        except Exception as e:
            raise Exception(f"Error updating user: {str(e)}")
    
    async def delete_user(self, user_id: str) -> bool:
        """Delete user by ID"""
        try:
            if not ObjectId.is_valid(user_id):
                return False

            collection = self._get_collection()
            result = await collection.delete_one({"_id": ObjectId(user_id)})
            return result.deleted_count > 0
            
        except Exception as e:
            raise Exception(f"Error deleting user: {str(e)}")
    
    async def get_users_count(self) -> int:
        """Get total number of users"""
        try:
            collection = self._get_collection()
            return await collection.count_documents({})
        except Exception as e:
            raise Exception(f"Error counting users: {str(e)}")

# Create service instance
user_service = UserService()
