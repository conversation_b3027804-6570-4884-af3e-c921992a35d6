# FastAPI MongoDB Application

Một ứng dụng hoàn chỉnh sử dụng FastAPI và MongoDB để quản lý người dùng.

## Tính năng

- ✅ CRUD operations cho User (Create, Read, Update, Delete)
- ✅ Kết nối MongoDB với Motor (async driver)
- ✅ Validation với Pydantic
- ✅ Error handling
- ✅ Environment configuration
- ✅ API documentation tự động với Swagger
- ✅ Health check endpoint
- ✅ CORS middleware
- ✅ Pagination support

## Cấu trúc dự án

```
FastAPI_MongoPass/
├── config/
│   └── database.py          # Cấu hình kết nối MongoDB
├── models/
│   └── user.py             # Pydantic models
├── routes/
│   └── user_routes.py      # API routes
├── services/
│   └── user_service.py     # Business logic
├── schemas/
│   └── user_schema.py      # MongoDB schemas
├── main.py                 # Entry point
├── requirements.txt        # Dependencies
├── .env                    # Environment variables
└── README.md              # Documentation
```

## Cài đặt

### 1. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 2. Cấu hình MongoDB

Đảm bảo MongoDB đang chạy trên máy của bạn. Nếu sử dụng MongoDB Compass:

1. Mở MongoDB Compass
2. Kết nối đến `mongodb://localhost:27017`
3. Tạo database mới tên `fastapi_mongodb` (hoặc sử dụng tên khác trong file .env)

### 3. Cấu hình Environment Variables

Chỉnh sửa file `.env` theo cấu hình của bạn:

```env
# MongoDB Configuration
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=fastapi_mongodb

# Application Configuration
APP_NAME=FastAPI MongoDB App
APP_VERSION=1.0.0
DEBUG=True
```

### 4. Chạy ứng dụng

```bash
# Chạy với uvicorn
uvicorn main:app --reload

# Hoặc chạy trực tiếp
python main.py
```

Ứng dụng sẽ chạy tại: http://localhost:8000

## API Endpoints

### Root & Health Check
- `GET /` - Root endpoint
- `GET /health` - Health check

### User Management
- `POST /api/v1/users/` - Tạo user mới
- `GET /api/v1/users/` - Lấy danh sách users (có pagination)
- `GET /api/v1/users/count` - Đếm tổng số users
- `GET /api/v1/users/{user_id}` - Lấy user theo ID
- `GET /api/v1/users/email/{email}` - Lấy user theo email
- `PUT /api/v1/users/{user_id}` - Cập nhật user
- `DELETE /api/v1/users/{user_id}` - Xóa user

## API Documentation

Sau khi chạy ứng dụng, bạn có thể truy cập:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Ví dụ sử dụng

### Tạo user mới

```bash
curl -X POST "http://localhost:8000/api/v1/users/" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Nguyễn Văn A",
       "email": "<EMAIL>",
       "password": "123456",
       "age": 25,
       "phone": "**********",
       "address": "Hà Nội, Việt Nam"
     }'
```

### Lấy danh sách users

```bash
curl -X GET "http://localhost:8000/api/v1/users/?skip=0&limit=10"
```

### Lấy user theo ID

```bash
curl -X GET "http://localhost:8000/api/v1/users/{user_id}"
```

### Cập nhật user

```bash
curl -X PUT "http://localhost:8000/api/v1/users/{user_id}" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Nguyễn Văn B",
       "age": 26
     }'
```

### Xóa user

```bash
curl -X DELETE "http://localhost:8000/api/v1/users/{user_id}"
```

## Lưu ý

1. **Bảo mật**: Trong môi trường production, hãy:
   - Hash password trước khi lưu vào database
   - Sử dụng JWT tokens cho authentication
   - Cấu hình CORS origins cụ thể
   - Sử dụng HTTPS

2. **Database**: Ứng dụng sẽ tự động tạo collection `users` trong database khi có dữ liệu đầu tiên.

3. **Error Handling**: Ứng dụng có xử lý lỗi cơ bản, bao gồm:
   - Validation errors
   - Duplicate email errors
   - Database connection errors
   - Not found errors

## Troubleshooting

### Lỗi kết nối MongoDB
- Đảm bảo MongoDB đang chạy
- Kiểm tra MONGODB_URL trong file .env
- Kiểm tra firewall và network settings

### Lỗi import modules
- Đảm bảo đã cài đặt tất cả dependencies: `pip install -r requirements.txt`
- Kiểm tra Python version (khuyến nghị Python 3.8+)

### Lỗi validation
- Kiểm tra format dữ liệu gửi lên API
- Xem chi tiết lỗi trong Swagger UI documentation
